/*
 * @Date         : 2025-01-14 10:00:00
 * @Description  : AI搜索相关API
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";

/**
 * AI搜索请求参数
 */
export interface AISearchRequest {
  query: string; // 用户输入的自然语言查询
  context?: string; // 上下文信息（可选）
}

/**
 * AI搜索响应结果
 */
export interface AISearchResponse {
  success: boolean;
  message?: string;
  data: {
    parsedFilters: {
      [key: string]: any; // 解析后的筛选条件
    };
    explanation: string; // 解析说明
    confidence: number; // 置信度 0-1
  };
}

/**
 * AI搜索历史记录
 */
export interface AISearchHistory {
  id: string;
  query: string;
  parsedFilters: { [key: string]: any };
  explanation: string;
  timestamp: number;
  userId: number;
}

/**
 * @description: AI自然语言搜索解析
 * @param {AISearchRequest} data 搜索请求参数
 * @returns {Promise<AISearchResponse>} 解析结果
 */
export const aiSearchParseApi = async (data: AISearchRequest): Promise<AISearchResponse> => {
  // 在开发环境使用模拟实现
  if (import.meta.env.DEV) {
    const { mockAISearchParse } = await import('/@/utils/aiSearchMock');
    return mockAISearchParse(data.query);
  }

  const response = await http.request<any>("post", `${baseURL.robot}/admin/customer/ai-search/parse`, {
    data
  });

  return {
    success: true,
    message: (response as any).message || '',
    data: (response as any).data || response
  };
};

/**
 * @description: 获取AI搜索历史记录
 * @param {object} params 查询参数
 * @returns {Promise<{list: AISearchHistory[], total: number}>} 历史记录列表
 */
export const getAISearchHistoryApi = (params: {
  page?: number;
  pageSize?: number;
  userId?: number;
}) => {
  return http.request<{
    list: AISearchHistory[];
    total: number;
  }>("get", `${baseURL.robot}/admin/customer/ai-search/history`, {
    params
  });
};

/**
 * @description: 保存AI搜索历史记录
 * @param {Omit<AISearchHistory, 'id' | 'timestamp'>} data 历史记录数据
 * @returns {Promise<{id: string}>} 保存结果
 */
export const saveAISearchHistoryApi = (data: Omit<AISearchHistory, 'id' | 'timestamp'>) => {
  return http.request<{id: string}>("post", `${baseURL.robot}/admin/customer/ai-search/history`, {
    data
  });
};

/**
 * @description: 删除AI搜索历史记录
 * @param {string} id 历史记录ID
 * @returns {Promise<void>} 删除结果
 */
export const deleteAISearchHistoryApi = (id: string) => {
  return http.request<void>("delete", `${baseURL.robot}/admin/customer/ai-search/history/${id}`);
};

/**
 * @description: 获取AI搜索提示文案
 * @returns {Promise<{tips: string[]}>} 提示文案列表
 */
export const getAISearchTipsApi = () => {
  return http.request<{tips: string[]}>("get", `${baseURL.robot}/admin/customer/ai-search/tips`);
};

/**
 * 常用的搜索示例
 */
export const AI_SEARCH_EXAMPLES = [
  "查找最近一周注册的用户",
  "找出付费金额大于1000元的客户",
  "搜索北京地区的高意向客户",
  "查看最近3天有外呼记录的客户",
  "找出已添加微信的客户",
  "搜索iPad用户中的高年级学生",
  "查找最近一个月没有拨通的客户",
  "找出有工单记录的客户"
];

/**
 * 字段映射配置 - 用于AI解析时的字段对应关系
 */
export const FIELD_MAPPING = {
  // 时间相关
  "注册时间": "regTime",
  "创建时间": "time",
  "最近拨打时间": "lastDialTime",
  "最近拨通时间": "lastDealing",
  "最近付费时间": "lastPaidTime",
  "看课时间": "watchTime",
  "线索到期时间": "clueTime",
  "权益到期时间": "authTime",

  // 金额相关
  "付费金额": "historyAmountMin",
  "订单金额": "amountMin",
  "历史付费": "historyAmountMin",

  // 地区相关
  "地区": "regionInfos",
  "省份": "regionInfos",
  "城市": "regionInfos",

  // 用户属性
  "意向度": "intention",
  "年级": "grade",
  "来源": "source",
  "渠道": "channel",
  "iPad用户": "isPadUser",
  "微信": "isAddWechat",
  "锁定": "isLock",
  "工单": "existTicket",

  // 通话相关
  "外呼次数": "callCount",
  "通话状态": "callState",

  // 其他
  "家庭ID": "familyId",
  "洋葱ID": "onionid",
  "手机号": "phone",
  "坐席": "workerid"
};
