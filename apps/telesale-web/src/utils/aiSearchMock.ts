/*
 * @Date         : 2025-01-14 11:00:00
 * @Description  : AI搜索模拟实现，用于演示和测试
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { FIELD_MAPPING, type AISearchResponse } from "/@/api/customer/aiSearch";

/**
 * 模拟AI搜索解析功能
 * 这是一个简化的实现，实际项目中应该调用真实的AI服务
 */
export function mockAISearchParse(query: string): Promise<AISearchResponse> {
  return new Promise(resolve => {
    // 模拟网络延迟
    setTimeout(() => {
      const result = parseQuery(query);
      resolve({
        success: true,
        data: result
      });
    }, 1000 + Math.random() * 2000); // 1-3秒的随机延迟
  });
}

/**
 * 简单的查询解析逻辑
 */
function parseQuery(query: string) {
  const parsedFilters: Record<string, any> = {};
  let explanation = "";
  let confidence = 0.8;

  // 时间相关解析
  const timePatterns = [
    { pattern: /最近(\d+)天/, field: "time", type: "recent_days" },
    { pattern: /最近(\d+)周/, field: "time", type: "recent_weeks" },
    { pattern: /最近(\d+)个?月/, field: "time", type: "recent_months" },
    { pattern: /今天/, field: "time", type: "today" },
    { pattern: /昨天/, field: "time", type: "yesterday" },
    { pattern: /本周/, field: "time", type: "this_week" },
    { pattern: /上周/, field: "time", type: "last_week" },
    { pattern: /本月/, field: "time", type: "this_month" },
    { pattern: /上月/, field: "time", type: "last_month" }
  ];

  // 金额相关解析
  const amountPatterns = [
    { pattern: /付费金额?大于(\d+)/, field: "historyAmountMin", type: "min" },
    { pattern: /付费金额?小于(\d+)/, field: "historyAmountMax", type: "max" },
    { pattern: /付费金额?(\d+)以上/, field: "historyAmountMin", type: "min" },
    { pattern: /付费金额?(\d+)以下/, field: "historyAmountMax", type: "max" },
    { pattern: /付费.*?(\d+)元?以上/, field: "historyAmountMin", type: "min" },
    { pattern: /付费.*?(\d+)元?以下/, field: "historyAmountMax", type: "max" }
  ];

  // 地区相关解析
  const regionPatterns = [
    { pattern: /北京/, code: "110000", name: "北京" },
    { pattern: /上海/, code: "310000", name: "上海" },
    { pattern: /广州/, code: "440100", name: "广州" },
    { pattern: /深圳/, code: "440300", name: "深圳" },
    { pattern: /杭州/, code: "330100", name: "杭州" },
    { pattern: /成都/, code: "510100", name: "成都" },
    { pattern: /武汉/, code: "420100", name: "武汉" },
    { pattern: /西安/, code: "610100", name: "西安" }
  ];

  // 用户属性解析
  const attributePatterns = [
    { pattern: /高意向|意向度高/, field: "intention", value: "A" },
    { pattern: /中意向|意向度中/, field: "intention", value: "B" },
    { pattern: /低意向|意向度低/, field: "intention", value: "C" },
    { pattern: /iPad用户/, field: "isPadUser", value: true },
    { pattern: /非iPad用户/, field: "isPadUser", value: false },
    { pattern: /已添加微信|加了微信/, field: "isAddWechat", value: true },
    { pattern: /未添加微信|没加微信/, field: "isAddWechat", value: false },
    { pattern: /锁定用户/, field: "isLock", value: true },
    { pattern: /有工单|提交过工单/, field: "existTicket", value: true },
    { pattern: /无工单|未提交工单/, field: "existTicket", value: false }
  ];

  const explanationParts: string[] = [];

  // 解析时间
  for (const timePattern of timePatterns) {
    const match = query.match(timePattern.pattern);
    if (match) {
      const timeRange = calculateTimeRange(
        timePattern.type,
        match[1] ? parseInt(match[1]) : 0
      );
      if (timeRange) {
        parsedFilters.time = timeRange;
        explanationParts.push(`时间范围：${formatTimeRange(timeRange)}`);
      }
      break;
    }
  }

  // 解析金额
  for (const amountPattern of amountPatterns) {
    const match = query.match(amountPattern.pattern);
    if (match) {
      const amount = parseInt(match[1]);
      parsedFilters[amountPattern.field] = amount;
      explanationParts.push(
        `${amountPattern.type === "min" ? "最小" : "最大"}付费金额：${amount}元`
      );
      break;
    }
  }

  // 解析地区
  for (const regionPattern of regionPatterns) {
    if (query.includes(regionPattern.pattern.source)) {
      parsedFilters.regionInfos = [
        {
          provinceCode: regionPattern.code,
          cityCodes: [regionPattern.code]
        }
      ];
      explanationParts.push(`地区：${regionPattern.name}`);
      break;
    }
  }

  // 解析用户属性
  for (const attrPattern of attributePatterns) {
    if (attrPattern.pattern.test(query)) {
      parsedFilters[attrPattern.field] = attrPattern.value;
      explanationParts.push(
        `${getFieldDisplayName(attrPattern.field)}：${getValueDisplayName(
          attrPattern.field,
          attrPattern.value
        )}`
      );
    }
  }

  // 解析外呼相关
  const callPatterns = [
    { pattern: /外呼次数?大于(\d+)/, field: "callCount", type: "min" },
    { pattern: /外呼次数?小于(\d+)/, field: "callCount", type: "max" },
    { pattern: /外呼次数?等于(\d+)/, field: "callCount", type: "equal" },
    { pattern: /没有?拨通|未拨通/, field: "callState", value: 0 },
    { pattern: /已拨通|拨通了/, field: "callState", value: 1 }
  ];

  for (const callPattern of callPatterns) {
    const match = query.match(callPattern.pattern);
    if (match) {
      if (callPattern.type === "equal") {
        parsedFilters[callPattern.field] = parseInt(match[1]);
        explanationParts.push(`外呼次数等于：${match[1]}次`);
      } else if (callPattern.type === "min") {
        parsedFilters[callPattern.field] = parseInt(match[1]);
        explanationParts.push(`外呼次数大于：${match[1]}次`);
      } else if (callPattern.type === "max") {
        parsedFilters[callPattern.field] = parseInt(match[1]);
        explanationParts.push(`外呼次数小于：${match[1]}次`);
      } else if (callPattern.value !== undefined) {
        parsedFilters[callPattern.field] = callPattern.value;
        explanationParts.push(
          `通话状态：${callPattern.value === 1 ? "已拨通" : "未拨通"}`
        );
      }
      break;
    }
  }

  // 生成解释文本
  if (explanationParts.length > 0) {
    explanation = `已为您解析出以下搜索条件：${explanationParts.join("；")}`;
  } else {
    explanation = "抱歉，暂时无法理解您的搜索需求，请尝试使用更具体的描述";
    confidence = 0.3;
  }

  // 根据解析结果调整置信度
  if (explanationParts.length >= 3) {
    confidence = 0.9;
  } else if (explanationParts.length >= 2) {
    confidence = 0.8;
  } else if (explanationParts.length === 1) {
    confidence = 0.7;
  }

  return {
    parsedFilters,
    explanation,
    confidence
  };
}

/**
 * 计算时间范围
 */
function calculateTimeRange(type: string, value: number) {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (type) {
    case "recent_days":
      return [
        today.getTime() - (value - 1) * 24 * 60 * 60 * 1000,
        now.getTime()
      ];
    case "recent_weeks":
      return [today.getTime() - value * 7 * 24 * 60 * 60 * 1000, now.getTime()];
    case "recent_months":
      const monthsAgo = new Date(today);
      monthsAgo.setMonth(monthsAgo.getMonth() - value);
      return [monthsAgo.getTime(), now.getTime()];
    case "today":
      return [today.getTime(), today.getTime() + 24 * 60 * 60 * 1000 - 1];
    case "yesterday":
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return [
        yesterday.getTime(),
        yesterday.getTime() + 24 * 60 * 60 * 1000 - 1
      ];
    default:
      return null;
  }
}

/**
 * 格式化时间范围显示
 */
function formatTimeRange(timeRange: number[]) {
  const start = new Date(timeRange[0]);
  const end = new Date(timeRange[1]);
  return `${start.toLocaleDateString()} 至 ${end.toLocaleDateString()}`;
}

/**
 * 获取字段显示名称
 */
function getFieldDisplayName(field: string) {
  const fieldNames: Record<string, string> = {
    intention: "意向度",
    isPadUser: "iPad用户",
    isAddWechat: "微信状态",
    isLock: "锁定状态",
    existTicket: "工单状态",
    callState: "通话状态"
  };
  return fieldNames[field] || field;
}

/**
 * 获取值显示名称
 */
function getValueDisplayName(field: string, value: any) {
  if (field === "intention") {
    const intentionMap: Record<string, string> = {
      A: "高意向",
      B: "中意向",
      C: "低意向"
    };
    return intentionMap[value] || value;
  }

  if (typeof value === "boolean") {
    if (field === "isPadUser") {
      return value ? "是" : "否";
    }
    if (field === "isAddWechat") {
      return value ? "已添加" : "未添加";
    }
    if (field === "isLock") {
      return value ? "已锁定" : "未锁定";
    }
    if (field === "existTicket") {
      return value ? "有工单" : "无工单";
    }
  }

  return String(value);
}
