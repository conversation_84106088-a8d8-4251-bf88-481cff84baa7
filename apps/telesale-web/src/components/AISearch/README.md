# AI搜索功能实现文档

## 功能概述

AI搜索功能是为客户池页面开发的智能搜索辅助工具，通过自然语言处理技术，将用户输入的自然语言查询转换为系统可识别的筛选条件，提高用户查找数据的效率。

## 功能特性

### 1. 自然语言解析
- 支持中文自然语言输入
- 智能识别时间、金额、地区、用户属性等查询条件
- 提供解析置信度评估

### 2. 智能交互
- 浮窗式交互界面
- 可收起/展开的洋葱头图标
- 搜索历史记录
- 搜索提示文案

### 3. 权限控制
- 通过 `v-auth="'telesale_admin_customer_ai_search'"` 控制访问权限
- 只有具备权限的用户才能看到AI搜索按钮

### 4. 埋点统计
- AI搜索点击埋点
- AI搜索发送埋点
- 自动搜索埋点（确认AI解析结果）
- 手动搜索埋点

## 文件结构

```
src/
├── components/AISearch/
│   ├── index.vue              # AI搜索主组件
│   └── README.md              # 说明文档
├── api/customer/
│   └── aiSearch.ts            # AI搜索API接口
├── utils/
│   └── aiSearchMock.ts        # AI搜索模拟实现
└── views/
    ├── customer/ongoing/components/
    │   └── NewSearch.vue      # 客户池搜索组件（已集成AI搜索）
    └── test/
        └── AISearchTest.vue   # AI搜索测试页面
```

## 使用方法

### 1. 在组件中使用

```vue
<template>
  <div>
    <!-- AI搜索按钮 -->
    <el-button 
      v-auth="'telesale_admin_customer_ai_search'"
      type="primary"
      @click="showAISearch"
    >
      <el-icon><Avatar /></el-icon>
      AI搜索
    </el-button>
    
    <!-- AI搜索组件 -->
    <AISearch
      v-model:visible="aiSearchVisible"
      @confirm="handleAISearchConfirm"
    />
  </div>
</template>

<script setup>
import AISearch from '/@/components/AISearch/index.vue';
import { Avatar } from '@element-plus/icons-vue';

const aiSearchVisible = ref(false);

const showAISearch = () => {
  aiSearchVisible.value = true;
};

const handleAISearchConfirm = (filters) => {
  // 处理AI解析的筛选条件
  console.log('AI解析结果:', filters);
  // 应用到搜索表单
  Object.assign(searchForm, filters);
  // 执行搜索
  onSearch();
};
</script>
```

### 2. 支持的搜索语法

#### 时间相关
- "最近一周注册的用户"
- "最近3天有外呼记录的客户"
- "今天创建的客户"
- "本月付费的用户"

#### 金额相关
- "付费金额大于1000元的客户"
- "历史付费500元以上的用户"
- "订单金额小于2000的客户"

#### 地区相关
- "北京地区的客户"
- "上海的高意向用户"
- "广州深圳的iPad用户"

#### 用户属性
- "高意向客户"
- "已添加微信的用户"
- "iPad用户"
- "有工单记录的客户"
- "锁定用户"

#### 通话相关
- "外呼次数大于5次的客户"
- "最近没有拨通的用户"
- "已拨通的客户"

## API接口

### 1. AI搜索解析
```typescript
aiSearchParseApi(data: AISearchRequest): Promise<AISearchResponse>
```

### 2. 搜索历史
```typescript
getAISearchHistoryApi(params): Promise<{list: AISearchHistory[], total: number}>
saveAISearchHistoryApi(data): Promise<{id: string}>
deleteAISearchHistoryApi(id: string): Promise<void>
```

### 3. 搜索提示
```typescript
getAISearchTipsApi(): Promise<{tips: string[]}>
```

## 埋点事件

### 1. 客户池-AI搜索
- **事件名**: `customerPool_aiSearch`
- **触发条件**: 点击AI搜索按钮
- **参数**: 操作人姓名、操作人时间、操作人部门、页面来源

### 2. 客户池-AI搜索-发送
- **事件名**: `customerPool_aiSearch_send`
- **触发条件**: 输入文字点击发送按钮
- **参数**: 操作人姓名、操作人时间、操作人部门、页面来源、搜索内容

### 3. 客户池-自动搜索
- **事件名**: `customerPool_autoSearch`
- **触发条件**: 确认AI解析结果后自动填充筛选条件
- **参数**: 操作人姓名、操作人时间、操作人部门、页面来源、搜索内容、解析结果

### 4. 客户池-手动搜索
- **事件名**: `customerPool_manualSearch`
- **触发条件**: 手动点击搜索按钮
- **参数**: 操作人姓名、操作人时间、操作人部门、页面来源、触发方式

## 开发说明

### 1. 模拟实现
在开发环境中，AI搜索使用本地模拟实现（`aiSearchMock.ts`），无需依赖真实的AI服务。

### 2. 生产环境
在生产环境中，需要配置真实的AI服务端点：
- 修改 `baseURL.robot` 指向正确的AI服务地址
- 确保AI服务实现了相应的API接口

### 3. 权限配置
需要在权限系统中添加 `telesale_admin_customer_ai_search` 权限，并分配给相应的用户角色。

### 4. 字段映射
AI解析结果的字段映射配置在 `aiSearch.ts` 的 `FIELD_MAPPING` 中，可根据实际需求调整。

## 测试

### 1. 功能测试
访问测试页面 `/test/AISearchTest` 进行功能验证。

### 2. 集成测试
在客户池页面测试完整的搜索流程。

### 3. 性能测试
验证AI解析的响应时间和准确性。

## 注意事项

1. **网络延迟**: AI解析可能需要1-3秒时间，已添加loading状态提示
2. **置信度**: 当AI解析置信度较低时，会显示警告提示
3. **历史记录**: 搜索历史会自动保存，便于用户重复使用
4. **错误处理**: 已添加完善的错误处理和用户提示
5. **权限控制**: 确保只有授权用户才能使用AI搜索功能

## 后续优化

1. **语义理解**: 增强自然语言理解能力
2. **上下文记忆**: 支持多轮对话和上下文引用
3. **个性化**: 根据用户习惯提供个性化搜索建议
4. **多语言**: 支持英文等其他语言
5. **语音输入**: 支持语音转文字输入
