<!--
 * @Date         : 2025-01-14 10:30:00
 * @Description  : AI搜索浮窗组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="ai-search-container" v-if="visible">
    <!-- 遮罩层 -->
    <div class="ai-search-overlay" @click="handleClose" />

    <!-- 浮窗主体 -->
    <div class="ai-search-popup" :class="{ collapsed: isCollapsed }">
      <!-- 头部 -->
      <div class="ai-search-header">
        <div class="header-left">
          <el-icon
            class="onion-icon"
            :class="{ collapsed: isCollapsed }"
            @click="toggleCollapse"
          >
            <Avatar />
          </el-icon>
          <span v-if="!isCollapsed" class="title">AI搜索</span>
        </div>
        <div class="header-right" v-if="!isCollapsed">
          <el-icon class="close-icon" @click="handleClose">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="ai-search-content" v-if="!isCollapsed">
        <!-- 提示文案 -->
        <div class="tips-section" v-if="!hasSearchHistory">
          <div class="tips-title">您可以这样搜索：</div>
          <div class="tips-list">
            <div
              v-for="(tip, index) in searchTips"
              :key="index"
              class="tip-item"
              @click="handleTipClick(tip)"
            >
              {{ tip }}
            </div>
          </div>
        </div>

        <!-- 搜索历史 -->
        <div class="history-section" v-if="hasSearchHistory">
          <div class="history-title">搜索记录</div>
          <div class="history-list">
            <div
              v-for="item in searchHistory"
              :key="item.id"
              class="history-item"
            >
              <div class="history-query">{{ item.query }}</div>
              <div class="history-explanation">{{ item.explanation }}</div>
              <div class="history-actions">
                <el-button size="small" @click="handleHistoryReuse(item)">
                  重新使用
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 解析结果 -->
        <div class="result-section" v-if="parseResult">
          <div class="result-bubble">
            <div class="result-explanation">
              {{ parseResult.explanation }}
            </div>
            <div class="result-confidence" v-if="parseResult.confidence < 0.8">
              <el-icon><Warning /></el-icon>
              <span>置信度较低，请确认解析结果是否正确</span>
            </div>
            <div class="result-actions">
              <el-button
                type="primary"
                size="small"
                @click="handleConfirm"
                :loading="confirmLoading"
              >
                确认
              </el-button>
              <el-button
                size="small"
                @click="handleReparse"
                :loading="reparseLoading"
              >
                重新解析
              </el-button>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-section">
          <div class="input-wrapper">
            <el-input
              v-model="searchQuery"
              type="textarea"
              :rows="2"
              placeholder="请输入您要搜索的条件，例如：查找最近一周注册的北京用户"
              @keyup.enter="handleSearch"
              :disabled="loading"
            />
            <el-button
              type="primary"
              class="send-button"
              @click="handleSearch"
              :loading="loading"
              :disabled="!searchQuery.trim()"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { Avatar, Close, Warning } from "@element-plus/icons-vue";
import {
  aiSearchParseApi,
  getAISearchHistoryApi,
  saveAISearchHistoryApi,
  getAISearchTipsApi,
  AI_SEARCH_EXAMPLES,
  type AISearchResponse,
  type AISearchHistory
} from "/@/api/customer/aiSearch";
import { useUserStoreHook } from "/@/store/modules/user";
import sendPointMath from "/@/utils/handle/sendPoint";

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "confirm", filters: Record<string, any>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const isCollapsed = ref(false);
const searchQuery = ref("");
const loading = ref(false);
const confirmLoading = ref(false);
const reparseLoading = ref(false);
const parseResult = ref<AISearchResponse["data"] | null>(null);
const searchHistory = ref<AISearchHistory[]>([]);
const searchTips = ref<string[]>(AI_SEARCH_EXAMPLES);

// 计算属性
const hasSearchHistory = computed(() => searchHistory.value.length > 0);

// 方法
const handleClose = () => {
  emit("update:visible", false);
  resetState();
};

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;

  // 埋点：点击洋葱头
  sendPointMath(
    { category: "customer", eventKey: "customerPool_aiSearch_toggle" },
    {
      操作人姓名: useUserStoreHook().userMsg.name,
      操作人时间: new Date().getTime(),
      操作人部门: "",
      页面来源: "客户池",
      操作类型: isCollapsed.value ? "收起" : "展开"
    }
  );
};

const handleSearch = async () => {
  if (!searchQuery.value.trim()) return;

  loading.value = true;

  // 埋点：AI搜索发送
  sendPointMath(
    { category: "customer", eventKey: "customerPool_aiSearch_send" },
    {
      操作人姓名: useUserStoreHook().userMsg.name,
      操作人时间: new Date().getTime(),
      操作人部门: "",
      页面来源: "客户池",
      搜索内容: searchQuery.value
    }
  );

  try {
    const response = await aiSearchParseApi({
      query: searchQuery.value.trim()
    });

    if (response.success) {
      parseResult.value = response.data;

      // 保存到历史记录
      await saveSearchHistory();
    } else {
      ElMessage.error(response.message || "AI解析失败，请重试");
    }
  } catch (error) {
    console.error("AI搜索失败:", error);
    ElMessage.error("搜索失败，请检查网络连接后重试");
  } finally {
    loading.value = false;
  }
};

const handleConfirm = async () => {
  if (!parseResult.value) return;

  confirmLoading.value = true;

  try {
    // 埋点：自动搜索（确认AI解析结果）
    sendPointMath(
      { category: "customer", eventKey: "customerPool_autoSearch" },
      {
        操作人姓名: useUserStoreHook().userMsg.name,
        操作人时间: new Date().getTime(),
        操作人部门: "",
        页面来源: "客户池",
        搜索内容: searchQuery.value,
        解析结果: JSON.stringify(parseResult.value.parsedFilters)
      }
    );

    // 将解析结果传递给父组件
    emit("confirm", parseResult.value.parsedFilters);

    // 关闭浮窗
    handleClose();

    ElMessage.success("搜索条件已应用");
  } catch (error) {
    console.error("确认搜索失败:", error);
    ElMessage.error("应用搜索条件失败");
  } finally {
    confirmLoading.value = false;
  }
};

const handleReparse = async () => {
  if (!searchQuery.value.trim()) return;

  reparseLoading.value = true;
  parseResult.value = null;

  try {
    await handleSearch();
  } finally {
    reparseLoading.value = false;
  }
};

const handleTipClick = (tip: string) => {
  searchQuery.value = tip;
  handleSearch();
};

const handleHistoryReuse = (item: AISearchHistory) => {
  searchQuery.value = item.query;
  parseResult.value = {
    parsedFilters: item.parsedFilters,
    explanation: item.explanation,
    confidence: 1.0
  };
};

const saveSearchHistory = async () => {
  if (!parseResult.value) return;

  try {
    await saveAISearchHistoryApi({
      query: searchQuery.value,
      parsedFilters: parseResult.value.parsedFilters,
      explanation: parseResult.value.explanation,
      userId: useUserStoreHook().userMsg.id as number
    });

    // 刷新历史记录
    await loadSearchHistory();
  } catch (error) {
    console.error("保存搜索历史失败:", error);
  }
};

const loadSearchHistory = async () => {
  try {
    const response = await getAISearchHistoryApi({
      page: 1,
      pageSize: 5,
      userId: useUserStoreHook().userMsg.id as number
    });

    searchHistory.value = (response as any).list || [];
  } catch (error) {
    console.error("加载搜索历史失败:", error);
  }
};

const loadSearchTips = async () => {
  try {
    const response = await getAISearchTipsApi();
    const tips = (response as any).tips;
    if (tips && tips.length > 0) {
      searchTips.value = tips;
    }
  } catch (error) {
    console.error("加载搜索提示失败:", error);
    // 使用默认提示
  }
};

const resetState = () => {
  searchQuery.value = "";
  parseResult.value = null;
  isCollapsed.value = false;
  loading.value = false;
  confirmLoading.value = false;
  reparseLoading.value = false;
};

// 监听visible变化
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      // 埋点：客户池AI搜索点击
      sendPointMath(
        { category: "customer", eventKey: "customerPool_aiSearch" },
        {
          操作人姓名: useUserStoreHook().userMsg.name,
          操作人时间: new Date().getTime(),
          操作人部门: "",
          页面来源: "客户池"
        }
      );

      loadSearchHistory();
    }
  }
);

// 组件挂载时加载数据
onMounted(() => {
  loadSearchTips();
});
</script>

<style lang="scss" scoped>
.ai-search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
}

.ai-search-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.ai-search-popup {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 400px;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
}

.ai-search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .onion-icon {
      font-size: 24px;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.collapsed {
        font-size: 28px;
      }
    }

    .title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .close-icon {
    font-size: 20px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.ai-search-content {
  padding: 16px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.tips-section,
.history-section {
  margin-bottom: 16px;

  .tips-title,
  .history-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .tips-list {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .tip-item {
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 6px;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }

  .history-list {
    .history-item {
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 8px;

      .history-query {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .history-explanation {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .history-actions {
        text-align: right;
      }
    }
  }
}

.result-section {
  margin-bottom: 16px;

  .result-bubble {
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #bae7ff;
    border-radius: 8px;

    .result-explanation {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .result-confidence {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #fa8c16;
      margin-bottom: 12px;
    }

    .result-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}

.input-section {
  .input-wrapper {
    position: relative;

    :deep(.el-textarea) {
      .el-textarea__inner {
        padding-right: 80px;
        resize: none;
      }
    }

    .send-button {
      position: absolute;
      right: 8px;
      bottom: 8px;
      height: 32px;
      padding: 0 16px;
    }
  }
}
</style>
