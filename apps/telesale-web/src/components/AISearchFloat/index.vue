<!--
 * @Date         : 2025-01-14 13:00:00
 * @Description  : AI搜索悬浮按钮组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<template>
  <div>
    <!-- AI搜索悬浮按钮 -->
    <div
      v-auth="'telesale_admin_customer_ai_search'"
      class="ai-search-float-button"
      :class="`position-${position}`"
      @click="handleAISearch"
      :title="title"
      :style="{ top: top }"
    >
      <!-- <img :src="iconSrc" :alt="title" class="ai-icon" />
      <div class="pulse-ring" /> -->
      111
    </div>

    <!-- AI搜索浮窗 -->
    <AISearch
      v-model:visible="isAISearchVisible"
      @confirm="handleAISearchConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import AISearch from "/@/components/AISearch/index.vue";
import sendPointMath from "/@/utils/handle/sendPoint";
import { useUserStoreHook } from "/@/store/modules/user";

interface Props {
  title?: string;
  iconSrc?: string;
  position?: "left" | "right";
  top?: string;
}

interface Emits {
  (e: "confirm", filters: Record<string, any>): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: "AI搜索",
  iconSrc: "/assets/favicon.ico",
  position: "left",
  top: "50%"
});

const emit = defineEmits<Emits>();

const isAISearchVisible = ref(false);

const handleAISearch = () => {
  isAISearchVisible.value = true;

  // 埋点：客户池AI搜索点击
  sendPointMath(
    { category: "customer", eventKey: "customerPool_aiSearch" },
    {
      操作人姓名: useUserStoreHook().userMsg.name,
      操作人时间: new Date().getTime(),
      操作人部门: "",
      页面来源: "客户池"
    }
  );
};

const handleAISearchConfirm = (filters: Record<string, any>) => {
  emit("confirm", filters);
};
</script>

<style lang="scss" scoped>
.ai-search-float-button {
  position: fixed;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  z-index: 1000;
  transition: all 0.3s ease;

  // 根据position属性设置位置
  &.position-left {
    left: 20px;
  }

  &.position-right {
    right: 20px;
  }

  &:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);

    .pulse-ring {
      animation: pulse 1.5s infinite;
    }
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  .ai-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    filter: brightness(1.2);
    position: relative;
    z-index: 2;
  }

  .pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    opacity: 0;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-search-float-button {
    width: 50px;
    height: 50px;

    &.position-left {
      left: 15px;
    }

    &.position-right {
      right: 15px;
    }

    .ai-icon {
      width: 26px;
      height: 26px;
    }

    .pulse-ring {
      width: 50px;
      height: 50px;
    }
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .ai-search-float-button {
    background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
    box-shadow: 0 4px 20px rgba(76, 99, 210, 0.4);

    &:hover {
      box-shadow: 0 6px 25px rgba(76, 99, 210, 0.6);
    }
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .ai-search-float-button {
    border: 2px solid #fff;

    .ai-icon {
      filter: brightness(1.5) contrast(1.2);
    }
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .ai-search-float-button {
    transition: none;

    &:hover {
      transform: translateY(-50%);
    }

    .pulse-ring {
      animation: none !important;
    }
  }

  @keyframes pulse {
    /* 禁用动画 */
  }
}
</style>
