<script setup lang="ts">
import { ref } from "vue";
import { FormInstance, ElMessage, UploadRequestOptions } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook, useUserStore } from "/@/store/modules/user";
import { applyClue, coolUpload } from "/@/api/customer";
import { useDetail } from "/@/utils/handle/customDetails";
import sendPointMath from "/@/utils/handle/sendPoint";
import ManualInput from "../dialog/ManualInput.vue";
import UploadDataEcho from "../dialog/UploadDataEcho.vue";
import UploadDataClue from "../dialog/UploadDataClue.vue";
import getHistoryMetric from "/@/utils/asyn/getHistoryMetric";
import FormOrgAgent from "/@/components/FormOrgAgent/index.vue";

import { BatchClueList, uploadClueApi } from "/@/api/customer/index";
import { openDayList } from "/@/utils/data/common";
import FormSearch from "/@/components/FormSearch/index.vue";

import { getAuth } from "/@/utils/auth";
import { cloneDeep } from "lodash-es";
import { CopyDocument } from "@element-plus/icons-vue";
import CitySelector from "/@/components/CitySelector/index.vue";
import SyncDatePicker from "/@/components/SyncDatePicker/index.vue";
import { activeList, haveOrNotList } from "../utils/data";
import { listHeader } from "../../done/utils/data";
import AISearchFloat from "/@/components/AISearchFloat/index.vue";

const { toDetail } = useDetail();
let device = useAppStoreHook().device;
const useUser = useUserStore();
const { setPointParams } = useUser;

interface Props {
  type?: number;
  loading: boolean;
}
const props = defineProps<Props>();

interface Emits {
  (e: "onSearch"): void;
  (e: "reset"): void;
  (e: "clearSort"): void;
  (e: "update:loading", val: boolean): void;
  (e: "update:dataMemory", val: any): void;
  (e: "transferMore"): void;
  (e: "saveView"): void;
}

const emit = defineEmits<Emits>();
const timeSortData = computed(() => {
  if (props.type === 2) {
    return listHeader.value.filter(item => item.isTimeSort);
  }
  return activeList.filter(item => item.isTimeSort);
});
const clueList = ref<BatchClueList[]>([]);
const isModalClue = ref<boolean>(false);

function onSearch() {
  // 埋点：手动搜索
  sendPointMath(
    { category: "customer", eventKey: "customerPool_manualSearch" },
    {
      操作人姓名: useUserStoreHook().userMsg.name,
      操作人时间: new Date().getTime(),
      操作人部门: "",
      页面来源: "客户池",
      触发方式: "手动搜索"
    }
  );

  emit("onSearch");
}
function uploadFile(file): any {
  let formData = new FormData();
  formData.append("file", file.file);
  emit("update:loading", true);
  coolUpload(formData)
    .then(({ data }: { data: any }) => {
      dataUpload.value = data;
      emit("update:loading", false);
      isModelUpload.value = true;
    })
    .catch(() => {
      emit("update:loading", false);
    });
}

function uploadUser(file: UploadRequestOptions): any {
  let formData = new FormData();
  formData.append("file", file.file);
  emit("update:loading", true);
  uploadClueApi(formData)
    .then(({ data }) => {
      clueList.value = data.batchReleaseInfos;
      emit("update:loading", false);
      isModalClue.value = true;
    })
    .catch(() => {
      emit("update:loading", false);
    });
}

let dataUpload = ref();
let isModelUpload = ref<boolean>(false);
let isModelManual = ref<boolean>(false);
const citySelectorRef = ref<InstanceType<typeof CitySelector>>();
let applyTimeBegin = ref(0);
let count = ref(6);

function commonPiontParamsSet(status, applyCount, failReason = "") {
  let applyClue = {
    status,
    failReason,
    applyCount,
    applyTimeBegin: applyTimeBegin.value,
    applyTimeEnd: new Date().getTime()
  };
  if (status === "2") {
    sendPointMath({ category: "site", eventKey: "clickApplyLeads" }, applyClue);
  } else {
    console.log(
      "申请成功的时间",
      new Date(applyClue.applyTimeEnd).toLocaleString()
    );
    setPointParams("applyClue", applyClue);
  }
}

function apply(applyCount) {
  //申请时的时间戳存储
  applyCount === 1 && (applyTimeBegin.value = new Date().getTime());
  emit("update:loading", true);
  applyClue()
    .then((res: any) => {
      if (res.status === 200) {
        if (res.data?.phone) {
          commonPiontParamsSet("1", applyCount);
          count.value = 6;
          emit("update:dataMemory", res);
          emit("update:loading", false);
          getAuth("telesale_admin_clue_distribute") && getHistoryMetric();
          toDetail(res.data, "ongoing", "");
        } else {
          count.value--;
          if (count.value > 0) {
            apply(++applyCount);
          } else {
            commonPiontParamsSet("2", applyCount, "接口申请重复次数超过限制");
            emit("update:loading", false);
            count.value = 6;
            ElMessage.error("申请失败");
          }
        }
      } else {
        commonPiontParamsSet("2", applyCount, "公海池数据暂时为空");
        emit("update:loading", false);
        count.value = 6;
        ElMessage.error("公海池数据暂时为空");
      }
    })
    .catch(({ response }: { response: any }) => {
      commonPiontParamsSet("2", applyCount, response.data);
      count.value = 6;
      emit("update:loading", false);
    });
}
function transferMore() {
  emit("transferMore");
}

//form查询
const fromData = {
  time: undefined,
  clueTime: undefined,
  clueStart: undefined,
  clueEnd: undefined,
  workerid: undefined,
  phone: undefined,
  intention: undefined,
  stage: undefined,
  isLock: undefined,
  existTicket: undefined,
  usertype: -1,
  source: undefined,
  onionid: undefined,
  familyId: undefined,
  grade: undefined,
  amountMin: undefined,
  amountMax: undefined,
  historyAmountMin: undefined,
  historyAmountMax: undefined,
  orderBy: undefined,
  sort: undefined,
  channel: undefined,
  isAddWechat: undefined,
  isPadUser: undefined,
  newExam: undefined,
  notDial: false,
  orgId: null,
  role: undefined,
  callState: undefined,
  watchTime: undefined,
  lastActiveStart: undefined,
  lastActiveEnd: undefined,
  callCount: undefined,
  openCal: 14,
  createTime: undefined,
  createAtMin: undefined,
  createAtMax: undefined,
  regionInfos: [],
  watchTimeRang: undefined,
  lastDialTime: undefined,
  regTime: undefined,
  lastDealing: undefined,
  lastPaidTime: undefined,
  lastDialStart: undefined,
  lastDialEnd: undefined,
  regTimeStart: undefined,
  regTimeEnd: undefined,
  lastDealingStart: undefined,
  lastDealingEnd: undefined,
  lastPaidTimeStart: undefined,
  lastPaidTimeEnd: undefined,
  giftExperience: undefined,
  purchaseStatus: undefined,
  authTime: undefined,
  authEndAtStart: undefined,
  authEndAtEnd: undefined,
  timeRang: {
    watchTime: undefined,
    time: undefined,
    createTime: undefined,
    clueTime: undefined,
    lastDialTime: undefined,
    regTime: undefined,
    lastDealing: undefined,
    lastPaidTime: undefined,
    authTime: undefined
  },
  combSort: [],
  is108Course: undefined
};

const searchForm = ref(cloneDeep(fromData));

const formRef = ref<FormInstance>();
const formOrgAgentRef = ref();

const resetForm = () => {
  formRef.value.resetFields();
  searchForm.value = cloneDeep(fromData);
  if (props.type === 2) {
    formOrgAgentRef.value.agentListReset();
    searchForm.value.usertype = undefined;
  } else {
    searchForm.value.usertype = -1;
  }
  citySelectorRef.value?.clear();
};

const resetFilter = () => {
  resetForm();
  emit("reset");
};

const saveView = () => {
  emit("saveView");
};

const handleAISearchConfirm = (filters: Record<string, any>) => {
  // 将AI解析的筛选条件应用到搜索表单
  Object.keys(filters).forEach(key => {
    if (Object.prototype.hasOwnProperty.call(searchForm.value, key)) {
      searchForm.value[key] = filters[key];
    }
  });

  // 触发搜索
  onSearch();

  // 埋点：手动搜索（通过AI搜索触发的搜索）
  sendPointMath(
    { category: "customer", eventKey: "customerPool_manualSearch" },
    {
      操作人姓名: useUserStoreHook().userMsg.name,
      操作人时间: new Date().getTime(),
      操作人部门: "",
      页面来源: "客户池",
      触发方式: "AI搜索"
    }
  );
};

const clearValue = (key: string) => {
  searchForm.value[key] = undefined;
};

const sortTime = () => {
  searchForm.value.orderBy = undefined;
  searchForm.value.sort = undefined;
  emit("clearSort");
  onSearch();
};

defineExpose({
  searchForm,
  resetForm,
  resetFilter
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="searchForm" class="clearfix">
    <FormSearch type="clear" @onSearch="onSearch" @onReset="resetFilter()">
      <template #show>
        <el-form-item prop="familyId">
          <el-input
            v-model="searchForm.familyId"
            placeholder="请输入家庭ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="onionid">
          <el-input
            v-model="searchForm.onionid"
            placeholder="请输入洋葱ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <FormOrgAgent
          ref="formOrgAgentRef"
          limitName="telesale_admin_done_group"
          workerId="workerid"
          v-model:form="searchForm"
        />
      </template>
      <template #hide>
        <el-form-item prop="watchTime">
          <SyncDatePicker
            v-model:value="searchForm.watchTime"
            v-model:rang="searchForm.timeRang.watchTime"
            dateRange="after"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="最近一次看课时间-开始"
            end-placeholder="最近一次看课时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="callCount">
          <div class="flex">
            <div class="input-prepend">外呼次数=</div>
            <el-input-number
              v-model="searchForm.callCount"
              :min="0"
              :max="99999999"
              :step="1"
              :precision="0"
              :controls="false"
              @keyup.enter="onSearch"
            />
          </div>
        </el-form-item>
        <el-form-item prop="amountMin" v-if="props.type === 2">
          <el-input-number
            v-model="searchForm.amountMin"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最小订单金额"
            :controls="false"
          />
          至
          <el-input-number
            v-model="searchForm.amountMax"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最大订单金额"
            :controls="false"
          />
        </el-form-item>
        <el-form-item prop="historyAmountMin">
          <el-input-number
            v-model="searchForm.historyAmountMin"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最小历史付费金额"
            :controls="false"
          />
          至
          <el-input-number
            v-model="searchForm.historyAmountMax"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最大历史付费金额"
            :controls="false"
          />
        </el-form-item>
        <el-form-item prop="time" v-if="props.type === 2">
          <SyncDatePicker
            v-model:value="searchForm.time"
            v-model:rang="searchForm.timeRang.time"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="成交时间-开始"
            end-placeholder="成交时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="time" v-if="props.type === 1">
          <SyncDatePicker
            v-model:value="searchForm.time"
            v-model:rang="searchForm.timeRang.time"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="创建时间-开始"
            end-placeholder="创建时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="createTime" v-if="props.type === 2">
          <SyncDatePicker
            v-model:value="searchForm.createTime"
            v-model:rang="searchForm.timeRang.createTime"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="创建时间-开始"
            end-placeholder="创建时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="clueTime">
          <SyncDatePicker
            dateRange="before"
            v-model:value="searchForm.clueTime"
            v-model:rang="searchForm.timeRang.clueTime"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="线索到期时间-开始"
            end-placeholder="线索到期时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="is108Course" v-if="props.type === 1">
          <el-select
            v-model="searchForm.is108Course"
            placeholder="请选择是否符合108课程"
            clearable
            @clear="clearValue('is108Course')"
          >
            <el-option label="符合" :value="true" />
            <el-option label="不符合" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item prop="isLock">
          <el-select
            v-model="searchForm.isLock"
            placeholder="请选择锁定用户"
            clearable
            @clear="clearValue('isLock')"
          >
            <el-option label="全部" :value="false" />
            <el-option label="锁定用户" :value="true" />
          </el-select>
        </el-form-item>

        <el-form-item prop="existTicket">
          <el-select
            v-model="searchForm.existTicket"
            placeholder="客服工单提交情况"
            clearable
            @clear="clearValue('existTicket')"
          >
            <el-option label="历史提交过工单" :value="true" />
            <el-option label="未提交过工单" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item prop="openCal">
          <div class="flex suffix">
            <el-select v-model="searchForm.openCal" @change="onSearch">
              <el-option
                v-for="item in openDayList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="input-suffix">家长打开学情报告次数</div>
          </div>
        </el-form-item>
        <el-form-item prop="regionInfos">
          <CitySelector
            ref="citySelectorRef"
            v-model:value="searchForm.regionInfos"
            :options="{ provinceKey: 'provinceCode', cityKey: 'cityCodes' }"
          />
        </el-form-item>
        <el-form-item prop="giftExperience">
          <el-select
            v-model="searchForm.giftExperience"
            placeholder="是否赠送体验课"
            clearable
            @clear="clearValue('giftExperience')"
          >
            <el-option
              v-for="item in haveOrNotList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item prop="purchaseStatus">
          <el-select
            v-model="searchForm.purchaseStatus"
            placeholder="用户购课情况"
            clearable
            multiple
            filterable
            collapse-tags
            @clear="clearValue('purchaseStatus')"
          >
            <el-option
              v-for="item in tabList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item prop="lastDialTime">
          <SyncDatePicker
            v-model:value="searchForm.lastDialTime"
            v-model:rang="searchForm.timeRang.lastDialTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次拨打时间-开始"
            end-placeholder="最近一次拨打时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="regTime" v-if="props.type === 1">
          <SyncDatePicker
            v-model:value="searchForm.regTime"
            v-model:rang="searchForm.timeRang.regTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="注册时间-开始"
            end-placeholder="注册时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="lastDealing">
          <SyncDatePicker
            v-model:value="searchForm.lastDealing"
            v-model:rang="searchForm.timeRang.lastDealing"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次拨通时间-开始"
            end-placeholder="最近一次拨通时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="lastPaidTime">
          <SyncDatePicker
            v-model:value="searchForm.lastPaidTime"
            v-model:rang="searchForm.timeRang.lastPaidTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次付费时间-开始"
            end-placeholder="最近一次付费时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="authTime">
          <SyncDatePicker
            v-model:value="searchForm.authTime"
            v-model:rang="searchForm.timeRang.authTime"
            type="datetimerange"
            value-format="x"
            start-placeholder="权益到期时间-开始"
            end-placeholder="权益到期时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
      </template>
      <template #btns>
        <TimeCombination
          v-if="timeSortData?.length > 0"
          v-model:value="searchForm.combSort"
          :data="timeSortData"
          @onSearch="sortTime"
        />

        <el-button
          v-auth="'telesale_admin_customer_view_save'"
          type="primary"
          :icon="CopyDocument"
          @click="saveView"
        >
          另存为
        </el-button>
      </template>
    </FormSearch>
    <el-form-item class="g-set-button">
      <template v-if="props.type === 1">
        <el-upload
          action="#"
          :http-request="uploadUser"
          :auto-upload="true"
          :show-file-list="false"
          accept=".xlsx"
          v-auth="'telesale_admin_custom_release_batch_clue'"
        >
          <el-button type="primary" class="g-margin-r-10">
            批量释放线索
          </el-button>
        </el-upload>
        <el-upload
          action="#"
          :http-request="uploadFile"
          :auto-upload="true"
          :show-file-list="false"
          accept=".xlsx"
          v-if="getAuth('telesale_admin_custom_release_upload')"
        >
          <el-button type="primary" class="g-margin-r-10">
            上传冷静期数据
          </el-button>
        </el-upload>
        <el-button
          type="primary"
          @click="isModelManual = true"
          v-if="getAuth('telesale_admin_custom_input')"
        >
          人工录入
        </el-button>
        <el-button
          type="primary"
          @click="apply(1)"
          v-if="getAuth('telesale_admin_custom_apply')"
        >
          申请客户
        </el-button>
      </template>
      <el-button
        type="primary"
        @click="transferMore"
        v-if="getAuth('telesale_admin_custom_transfer') && device !== 'mobile'"
      >
        批量转线索
      </el-button>
    </el-form-item>
    <UploadDataEcho
      :list="dataUpload"
      v-model:value="isModelUpload"
      @onSearch="onSearch"
    />
    <UploadDataClue
      v-if="isModalClue"
      :list="clueList"
      v-model:value="isModalClue"
      @onSearch="onSearch"
    />
    <ManualInput
      v-model:value="isModelManual"
      @onSearch="onSearch"
      v-if="isModelManual"
    />

    <!-- AI搜索悬浮按钮 -->
    <AISearchFloat @confirm="handleAISearchConfirm" />
  </el-form>
</template>

<style lang="scss" scoped>
.input-prepend {
  background-color: #f5f7fa;
  color: #909399;
  padding: 0 20px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
    0 -1px 0 0 #dcdfe6 inset;
}
.suffix {
  .input-suffix {
    background-color: #f5f7fa;
    color: #909399;
    padding: 0 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    box-shadow: -1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
      0 -1px 0 0 #dcdfe6 inset, 0 0 1px 0 #dcdfe6 inset;
  }
  :deep(.el-input) {
    width: 150px;
  }
}
</style>
