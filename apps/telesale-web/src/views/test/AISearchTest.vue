<!--
 * @Date         : 2025-01-14 12:00:00
 * @Description  : AI搜索功能测试页面
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="ai-search-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>AI搜索功能测试</span>
        </div>
      </template>

      <div class="test-content">
        <div class="test-section">
          <h3>测试说明</h3>
          <p>这是AI搜索功能的测试页面，您可以在这里测试AI搜索的各种功能。</p>

          <h4>支持的搜索示例：</h4>
          <ul>
            <li>查找最近一周注册的用户</li>
            <li>找出付费金额大于1000元的客户</li>
            <li>搜索北京地区的高意向客户</li>
            <li>查看最近3天有外呼记录的客户</li>
            <li>找出已添加微信的客户</li>
            <li>搜索iPad用户中的高年级学生</li>
            <li>查找最近一个月没有拨通的客户</li>
            <li>找出有工单记录的客户</li>
          </ul>
        </div>

        <div class="test-section">
          <h3>当前搜索条件</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item
              v-for="(value, key) in currentFilters"
              :key="key"
              :label="key"
            >
              {{ formatFilterValue(value) }}
            </el-descriptions-item>
          </el-descriptions>

          <div v-if="Object.keys(currentFilters).length === 0" class="empty-state">
            暂无搜索条件，请点击AI搜索按钮进行搜索
          </div>
        </div>

        <div class="test-section">
          <h3>操作区域</h3>
          <p>AI搜索悬浮按钮已显示在页面左侧，点击即可使用。</p>

          <el-button
            @click="clearFilters"
          >
            清空条件
          </el-button>

          <el-button
            @click="simulateSearch"
            :disabled="Object.keys(currentFilters).length === 0"
          >
            模拟搜索
          </el-button>
        </div>

        <div class="test-section" v-if="searchResult">
          <h3>搜索结果</h3>
          <el-alert
            :title="`搜索完成，共找到 ${searchResult.total} 条记录`"
            type="success"
            :closable="false"
          />

          <div class="result-details">
            <p><strong>搜索条件：</strong>{{ searchResult.conditions }}</p>
            <p><strong>执行时间：</strong>{{ searchResult.timestamp }}</p>
          </div>
        </div>
      </div>
    </el-card>

    <!-- AI搜索悬浮按钮 -->
    <AISearchFloat
      position="right"
      top="60%"
      @confirm="handleAISearchConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import AISearchFloat from '/@/components/AISearchFloat/index.vue';

// 响应式数据
const currentFilters = reactive<Record<string, any>>({});
const searchResult = ref<{
  total: number;
  conditions: string;
  timestamp: string;
} | null>(null);

// 方法
const handleAISearchConfirm = (filters: Record<string, any>) => {
  // 清空当前条件
  Object.keys(currentFilters).forEach(key => {
    delete currentFilters[key];
  });

  // 设置新条件
  Object.assign(currentFilters, filters);

  ElMessage.success('AI搜索条件已应用');

  // 自动执行搜索
  simulateSearch();
};

const clearFilters = () => {
  Object.keys(currentFilters).forEach(key => {
    delete currentFilters[key];
  });
  searchResult.value = null;
  ElMessage.info('搜索条件已清空');
};

const simulateSearch = () => {
  // 模拟搜索过程
  const conditions = Object.entries(currentFilters)
    .map(([key, value]) => `${key}: ${formatFilterValue(value)}`)
    .join(', ');

  // 模拟随机结果数量
  const total = Math.floor(Math.random() * 1000) + 1;

  searchResult.value = {
    total,
    conditions,
    timestamp: new Date().toLocaleString()
  };

  ElMessage.success(`搜索完成，找到 ${total} 条记录`);
};

const formatFilterValue = (value: any): string => {
  if (Array.isArray(value)) {
    if (value.length === 2 && typeof value[0] === 'number' && typeof value[1] === 'number') {
      // 时间范围
      return `${new Date(value[0]).toLocaleDateString()} 至 ${new Date(value[1]).toLocaleDateString()}`;
    }
    return JSON.stringify(value);
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否';
  }

  if (typeof value === 'object' && value !== null) {
    return JSON.stringify(value);
  }

  return String(value);
};
</script>

<style lang="scss" scoped>
.ai-search-test {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
  }

  .test-content {
    .test-section {
      margin-bottom: 30px;

      h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
      }

      h4 {
        color: #666;
        margin: 10px 0;
        font-size: 14px;
        font-weight: 500;
      }

      p {
        color: #666;
        line-height: 1.6;
        margin-bottom: 10px;
      }

      ul {
        color: #666;
        padding-left: 20px;

        li {
          margin-bottom: 5px;
          line-height: 1.5;
        }
      }

      .empty-state {
        text-align: center;
        color: #999;
        padding: 40px 0;
        background: #f9f9f9;
        border-radius: 4px;
      }

      .result-details {
        margin-top: 15px;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;

        p {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .el-button {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
